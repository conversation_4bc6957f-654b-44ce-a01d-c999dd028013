<!--#include file="data.asp"-->
<%
function sayfaK(sayfa)
	say=lcase(Request.ServerVariables("SCRIPT_NAME"))
	if instr(1,say,sayfa) > 0 then
		sayfaK=true
	else
		sayfaK=false
	end if
end function
sub header
suz=sec.gint("suz")
%>
<!DOCTYPE html><!--
Template Name: Conquer Responsive Admin Dashboard Template build with Twitter Bootstrap 2.3.1
-->
<!--[if IE 8]> <html lang="en" class="ie8"> <![endif]-->
<!--[if IE 9]> <html lang="en" class="ie9"> <![endif]-->
<!--[if !IE]><!-->
<html lang="en">
<!--<![endif]-->
<!-- BEGIN HEAD -->
<head>
<!-- END COPYRIGHT --> 
<!-- BEGIN JAVASCRIPTS(Load javascripts at bottom, this will reduce page load time) --> 
<!-- <PERSON><PERSON><PERSON> CORE PLUGINS --> 
                               
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title><%=ayargetir("firma")%></title>
<meta content='width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no' name='viewport'>
<!-- Bootstrap 3.3.2 -->
<link href="PnlPlugins/bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />    
<script src="https://kit.fontawesome.com/6f522336f0.js" crossorigin="anonymous"></script>
<!-- Ionicons 2.0.0 -->
<link href="PnlBootstrap/css/ionicons.min.css" rel="stylesheet" type="text/css" />    
<link href="PnlBootstrap/css/Med.min.css" rel="stylesheet" type="text/css" />
<link href="PnlBootstrap/css/_all-skins.min.css" rel="stylesheet" type="text/css" />
<link href="PnlPlugins/iCheck/flat/blue.css" rel="stylesheet" type="text/css" />
<link href="PnlPlugins/morris/morris.css" rel="stylesheet" type="text/css" />
<link href="PnlPlugins/jvectormap/jquery-jvectormap-1.2.2.css" rel="stylesheet" type="text/css" />
<link href="PnlPlugins/datepicker/datepicker3.css" rel="stylesheet" type="text/css" />
<link href="PnlPlugins/daterangepicker/daterangepicker-bs3.css" rel="stylesheet" type="text/css" />
<link href="PnlPlugins/bootstrap-wysihtml5/bootstrap3-wysihtml5.min.css" rel="stylesheet" type="text/css" />
<link href="PnlBootstrap/css/style.css" rel="stylesheet" type="text/css" />
<link rel="stylesheet" href="css/flag-icon.css">
<script src="PnlPlugins/jQuery/jQuery-2.1.3.min.js"></script>
</head>

<body class="skin-black">
	 <style>
        #loading {
            position: fixed;
            left: 0px;
            top: 0px;
            width: 100%;
            height: 100%;
            z-index: 9999;
            background: url(images/35.gif)center no-repeat #fff;
			display:none;
        }
    </style>

    <div id="loading"></div>

    <div class="wrapper">
      
      <header class="main-header">
        <!-- Header Navbar: style can be found in header.less -->
        <nav class="navbar navbar-static-top" role="navigation">
          <!-- Sidebar toggle button-->
          <a href="#" class="sidebar-toggle" data-toggle="offcanvas" role="button" style="font-size:20px;">
            <i class="fas fa-bars"></i>
          </a>
          <div class="navbar-custom-menu">
            <ul class="nav navbar-nav">
              <!-- User Account: style can be found in dropdown.less -->
              <li class="dropdown user user-menu">
                <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                  <i class="far fa-user-circle fs20"></i> &nbsp;
                  <span style="font-size:12px;"><%=ayargetir("firma")%> <strong>(<%=kullaniciGetir("kull")%>)</strong><span class="caret"></span></span>
                </a>
                <ul class="dropdown-menu">
                  <!-- User image -->
                  <li class="user-header">
                    <i class="fa fa-user beyaz fs25 mt20" aria-hidden="true"></i>
                    <p>
                      <%=kullaniciGetir("kull")%>
                    </p>
                  </li>
                  <!-- Menu Footer-->
                  <li class="user-footer">
                    <div class="pull-left">
                      <a href="ayar.asp?act=tumu&deg=kutu2" class="btn btn-default btn-flat"><i class="fa fa-cog" aria-hidden="true"></i> &nbsp;Site Ayarları</a>
                    </div>
                    <div class="pull-right">
                      <a href="cikis.asp" class="btn btn-default btn-flat"><i class="fas fa-sign-out-alt"></i> &nbsp;Çıkış</a>
                    </div>
                  </li>
                </ul>
              </li>
            </ul>
          </div>
        </nav>
      </header>
      <!-- Left side column. contains the logo and sidebar -->
      <%deg=sec.g("deg")%>
      <aside class="main-sidebar" >
        <!-- sidebar: style can be found in sidebar.less -->
        <section class="sidebar" style="margin-top:15px;">
          <!-- sidebar menu: : style can be found in sidebar.less -->
          <ul class="sidebar-menu">
            <li class="<%if deg="kutu1" then%> active <%end if%>">
              <a href="default.asp?deg=kutu1">
                <i class="fa fa-home" aria-hidden="true"></i> <span>Ana Sayfa</span>
              </a>
            </li>
            <%if yetkiGetir(loginID,"bildirimadresleri") then%>
            <li class="<%if deg="kutu4" then%> active <%end if%>">
              <a href="mailler.asp?act=tumu&deg=kutu4">
                <i class="fas fa-th-list"></i> <span>Bildirim Adresleri</span>
              </a>
            </li>
            <%end if%>


            <%if yetkiGetir(loginID,"dinamikformlar") then%>
            <li class="<%if deg="kutudnmk" or deg="kutudnmk1" or deg="kutudnmk2" or deg="kutudnmk3" then%>treeview active <%else%>treeview<%end if%>">
              <a href="#">
                <i class="fa fa-file-alt" aria-hidden="true"></i> <span>Dinamik Formlar</span>
                <i class="fa fa-angle-left pull-right"></i>
              </a>
              <ul class="treeview-menu">
                <li class="<%if deg="kutudnmk1" then%> active <%else%> treeview <%end if%>"><a href="iletisim.asp?act=tumu&deg=kutudnmk1"><i class="fas fa-circle text-info fs7"></i> &nbsp; İletişim Formu</a></li>                               
              </ul>
            </li>  
            <%end if%>
                                        
            <%if yetkiGetir(loginID,"menuler") then%>
            <li class="<%if sayfaT=1 then%> active <%end if%>">
              <a href="sayfalar.asp?act=tumu&sayfaT=1">
                <i class="fas fa-list"></i> <span> Menüler</span>
              </a>
            </li>
            <li class="<%if sayfaT=5 then%> active <%end if%>">
              <a href="sayfalar.asp?act=tumu&sayfaT=5">
                <i class="fas fa-list"></i> <span> Alt Menüler</span>
              </a>
            </li>         
            <li class="<%if sayfaT=3 then%> active <%end if%>">
              <a href="sayfalar.asp?act=tumu&sayfaT=3">
                <i class="fas fa-list"></i> <span> Anasayfa Sabit Yazılar</span>
              </a>
            </li>
            <li class="<%if sayfaT=4 then%> active <%end if%>">
              <a href="sayfalar.asp?act=tumu&sayfaT=4">
                <i class="fas fa-list"></i> <span> Blog</span>
              </a>
            </li>
            <li class="<%if sayfaT=4 then%> active <%end if%>">
              <a href="sayfalar.asp?act=tumu&sayfaT=6">
                <i class="fas fa-list"></i> <span> Anasyfa Kategoriler</span>
              </a>
            </li>            
            <%end if%>          
            <%if yetkiGetir(loginID,"anaslayt") then%>
            <li class="<%if sayfaT=2 then%> active <%end if%>">
              <a href="sayfalar.asp?act=tumu&sayfaT=2">
                <i class="fas fa-th-list"></i> <span>Anasayfa Slayt</span>
              </a>
            </li>
            <%end if%>                 
            
            <%if yetkiGetir(loginID,"siteyonetimi") then%>
            <li class="<%if deg="kutu5" or deg="kutu6" then%>treeview active <%else%>treeview<%end if%>">
              <a href="#">
                <i class="fa fa-cog" aria-hidden="true"></i> <span>Site Yönetimi</span>
                <i class="fa fa-angle-left pull-right"></i>
              </a>
              <ul class="treeview-menu">
                <li class="<%if deg="kutu5" then%> active <%else%> treeview <%end if%>"><a href="ayar.asp?act=tumu&deg=kutu5"><i class="fas fa-circle text-info fs7"></i> &nbsp; Site Ayarları</a></li>
                <li class="<%if deg="kutu6" then%> active <%else%> treeview <%end if%>"><a href="edit.asp?act=tumu&deg=kutu6"><i class="fas fa-circle text-info fs7"></i> &nbsp; Kullanıcı Adı & Şifre</a></li>
              </ul>
            </li>
            <%end if%>
          </ul>
        </section>
        <!-- /.sidebar -->
      </aside>

      <!-- Right side column. Contains the navbar and content of the page -->
    
  
<%end sub%>
 
    </div><!-- ./wrapper -->
<%sub footer%>

<!-- jQuery UI 1.11.2 -->
<script src="PnlBootstrap/js/jquery-ui.min.js" type="text/javascript"></script>
<!-- Resolve conflict in jQuery UI tooltip with Bootstrap tooltip -->
<script>
  $.widget.bridge('uibutton', $.ui.button);
</script>
<!-- Bootstrap 3.3.2 JS -->
<script src="PnlBootstrap/js/bootstrap.min.js" type="text/javascript"></script>    
<!-- Morris.js charts -->
<script src="PnlBootstrap/js/bootstrap-confirmation.js"></script>
<script>
$('[data-toggle=confirmation]').confirmation();
        $('[data-toggle=confirmation-singleton]').confirmation({ singleton: true });
        $('[data-toggle=confirmation-popout]').confirmation({ popout: true });

        
</script>
<script src="PnlBootstrap/js/raphael-min.js"></script>
<script src="PnlPlugins/morris/morris.min.js" type="text/javascript"></script>
<!-- Sparkline -->
<script src="PnlPlugins/sparkline/jquery.sparkline.min.js" type="text/javascript"></script>
<!-- jvectormap -->
<script src="PnlPlugins/jvectormap/jquery-jvectormap-1.2.2.min.js" type="text/javascript"></script>
<script src="PnlPlugins/jvectormap/jquery-jvectormap-world-mill-en.js" type="text/javascript"></script>
<!-- jQuery Knob Chart -->
<script src="PnlPlugins/knob/jquery.knob.js" type="text/javascript"></script>
<!-- daterangepicker -->
<script src="PnlPlugins/daterangepicker/daterangepicker.js" type="text/javascript"></script>
<!-- datepicker -->
<script src="PnlPlugins/datepicker/bootstrap-datepicker.js" type="text/javascript"></script>
<!-- Bootstrap WYSIHTML5 -->
<!-- iCheck -->
<script src="PnlPlugins/iCheck/icheck.min.js" type="text/javascript"></script>
<!-- Slimscroll -->
<script src="PnlPlugins/slimScroll/jquery.slimscroll.min.js" type="text/javascript"></script>
<script src="PnlJS/js/app.min.js" type="text/javascript"></script>
<script src="PnlPlugins/bootstrap-wysihtml5/bootstrap3-wysihtml5.all.min.js" type="text/javascript"></script>
<script>
	$(window).load(function () {
		$("#loading").fadeOut("slow");
	});

  $(function () {
	//Date range picker
	$('#reservation').datepicker({singleDatePicker: false});
	$('#reservation1').datepicker({singleDatePicker: false});
  });
</script>
</body>
<!-- END BODY -->
</html>
<%end sub%>

<%sub diltab%>
<ul class="nav nav-tabs" role="tablist">
  <%set dilg=bag.execute("select * from dil order by id")
  while not dilg.eof%>
<li <%if dilg("kod")="tr" then %>class="active"<%end if%>><a href="#<%=dilg("kod")%>" role="tab" data-toggle="tab"><%=dilg("bayrak")%>&nbsp;<%=dilg("dil")%></a></li>
<%dilg.movenext
wend%>
</ul>
<%end sub%>