﻿<!--#include file="view.asp"-->
<%
id=6
set duy1=bag.execute("select * from sayfalar where sayfaTIP=1 and id="&id&" and aktifpasif=true order by sira")
set kbaslik=bag.execute("select * from sayfalar where sayfaTIP=1 and id="& duy1("uid") &" and aktifpasif=true order by sira")

if not kbaslik.eof then
    set kust=bag.execute("select * from sayfalar where sayfaTIP=1 and id="& kbaslik("uid") &" and aktifpasif=true order by sira")
end if


if duy1("title_"&dil)<>"" then
title=duy1("title_"&dil)
else
title=duy1("baslik_"&dil)
end if

if duy1("description_"&dil)<>"" then
description1=duy1("description_"&dil)
else
description1=duy1("baslik_"&dil)
end if

url="/"& duy1("seo_"& dil)

if duy1("resim1")<>"" then
    resimyol=duy1("resim1")
else
    resimyol=ayargetir("resim2")
end if

call header%>

<div>
    <img src="/<%=resimyol%>" alt="Kayseri Beyaz Eşya Servisi - İklim Servis Profesyonel Tamir Hizmeti" class="uk-width-1-1 br-20 uk-box-shadow-xlarge" title="Kayseri Beyaz Eşya Servisi İletişim" loading="eager">
</div>

<div class="uk-container uk-margin-medium-top">
    <h1 class="fs30 uk-text-center"><%=duy1("baslik_"&dil)%></h1>
    <div>
        <ul class="uk-breadcrumb uk-text-center">
            <li itemprop="itemListElement" itemscope itemtype="http://schema.org/ListItem">
                <a itemscope itemtype="http://schema.org/Thing" id="1" itemprop="item" href="/" title="Anasayfa">
                    <span itemprop="name">Anasayfa</span>
                </a>
                <meta itemprop="position" content="1">
            </li>
            <li itemprop="itemListElement" itemscope itemtype="http://schema.org/ListItem">
                <a itemscope itemtype="http://schema.org/Thing" id="2" itemprop="item" href="/<%=duy1("seo_"& dil)%>" title="<%=duy1("baslik_"&dil)%>">
                    <span itemprop="name"><%=duy1("baslik_"&dil)%></span>
                </a>
                <meta itemprop="position" content="2">
            </li>
        </ul>
    </div>
</div><hr>
<div class="uk-container uk-container-large uk-margin-large-top uk-margin-large-bottom">
    <div uk-scrollspy="cls: uk-animation-fade; target: div; delay: 100; repeat: false;"><%=duy1("icerik_"&dil)%></div>
    <div uk-grid>						
        <div class="uk-width-expand@m">
            <div class="uk-margin-medium-top">
                <div class="uk-margin-medium-top uk-margin-large-left@s">
                    <div class="fs18"><span class="uk-margin-small-right text-red" uk-icon="icon:phone;ratio: 2"></span> Telefon</div>
                    <div class="uk-text-large uk-margin-small-top">
                      <div><a href="tel:<%=ayargetir("telefon")%>" class="hover-top uk-display-inline-block" aria-label="Telefon" title="Telefon"><%=ayargetir("telefon")%></a></div>
                      <div><a href="tel:<%=ayargetir("cep")%>" class="hover-top uk-display-inline-block" aria-label="GSM" title="GSM"><%=ayargetir("cep")%></a></div>
                    </div>
                </div>
                <div class="uk-margin-medium-top">
                    <div class="fs18"><span class="uk-margin-small-right text-red uk-text-large" uk-icon="icon:location;ratio: 2"></span> Adres</div>
                    <div class="uk-text-medium uk-margin-small-top">
                        <div><%=ayargetir("adres")%></div>
                    </div>
                </div>
                <div class="uk-margin-medium-top uk-margin-large-left@s">
                    <div class="fs18"><span class="uk-margin-small-right text-red" uk-icon="icon:mail;ratio: 2"></span> E-mail</div>
                    <div class="uk-text-large uk-margin-small-top">
                      <div><%=ayargetir("mail")%></div>
                    </div>
                </div>						  
            </div>
        </div>
        <div class="uk-width-expand@m"><%=ayargetir("maps")%></div>
    </div>
</div>
<%call footer%>