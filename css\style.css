@media (max-width:320px){
  
}
@media (min-width:321px){
  .logo{ background: url('/images/logo.png') no-repeat center left; width: 150px; background-size: 130px; height:84px; display: block;}
  .sticky {
    position: fixed !important;
    z-index:999;
    margin-top: 0px;
    -moz-transition: all 0.2s ease-out;  	-o-transition: all 0.2s ease-out;  	-webkit-transition: all 0.2s ease-out;  	-ms-transition: all 0.2s ease-out;
  background:#fff;
    box-shadow: 10px 10px 22px #d6d6d6; 
    }
    .container-padding{margin-top: 100px; padding: 0px;}
     .service-title{font-size:18px; line-height: 18px;}
}
@media(min-width:640px){

}
@media(min-width:960px){
  .uk-navbar-nav>li>a{ color: #000; font-size: 14px; font-weight: 500; margin: 50px 1px 0 1px;}
  .logo{ background: url('/images/logo.png') no-repeat center center; width: 160px; background-size: 160px; height:78px; display: block;}
  .sticky {
    position: fixed !important;
    z-index:999;
    margin-top: -50px;
    -moz-transition: all 0.2s ease-out;  	-o-transition: all 0.2s ease-out;  	-webkit-transition: all 0.2s ease-out;  	-ms-transition: all 0.2s ease-out;
  background:#fff;
    box-shadow: 10px 10px 22px #d6d6d6; 
    }
  .container-padding{margin-top: 150px; padding: 0 0 0 50px;}
  .service-title{font-size: 23px; line-height: 28px;}
}
@media(min-width:1200px){
  .uk-navbar-nav>li>a{ color: #000; font-size: 16px; font-weight: 500; margin: 50px 5px 0 5px;}
  .logo{ background: url('/images/logo.png') no-repeat center center; width: 210px; background-size: 210px; height:78px; display: block;}
}


@media(min-width:1400px){
 
}

.body-gradiend{background: #E6E6E6;background: linear-gradient(171deg, rgb(196, 196, 196) 0%, rgba(255, 255, 255, 1) 37%, rgba(255, 255, 255, 1) 100%);min-height: 260px; position: fixed; top: 0; left: 0; width: 100%; z-index: -1;}
.lef-white-bg{background: #fff; width: 100%; position: absolute; border-radius: 30px; top: 50px; box-shadow: 0px 50px 40px #e0e0e0;}
.sc-mt{margin-top: 150px; width: 100%;}
.sc-position{position: absolute !important; top: -100px !important;}

.sc-mt-mobil{margin-top: 60px; width: 100%;}
.sc-position-mobil{position: absolute !important; top: -170px !important;}

.ic-title{font-size: 30px; font-weight: 600; color: #000;margin:40px 0 10px 0; text-align: center;}
/* Butonun temel stilleri */

.servis-cagir-btn-mobil {   width: 150px !important; /* Buton boyutu biraz büyütüldü */
  height: 150px !important; }
.servis-cagir-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 200px; /* Buton boyutu biraz büyütüldü */
  height: 200px;
  padding: 10px; /* İç boşluklar, border ile daha iyi uyum için ayarlandı */
  font-size: 24px; /* Yazı boyutu büyütüldü */
  font-weight: bold;
  line-height: 30px;
  color: #ffffff !important; /* Yazı rengi */
  text-decoration: none;
  cursor: pointer;
  border-radius: 50%; /* Yuvarlak şekil */
  position: relative; /* Animasyon elemanları için referans noktası */
  overflow: hidden; /* Taşmaları gizle */
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3); /* Hafif gölge eklendi */
  transition: transform 0.3s ease, box-shadow 0.3s ease; /* Hover geçişleri */
  z-index: 1; /* Diğer içeriklerin üzerinde kalması için */
  text-align: center; /* Metni ortalar */
  box-sizing: border-box; /* Padding ve border boyuta dahil */

  /* Kendi kendine sürekli dönen ve renk değiştiren gradient */
  background: linear-gradient(135deg, #2f0c8e, #0a3dee, #2ab318, #0adbd9, #2f0c8e);
  background-size: 400% 400%; /* Gradientin daha büyük olması için */
  animation: gradientShift 15s ease infinite; /* Yumuşak renk geçişi animasyonu */
}

/* Animasyonlu "yıldız kayması" veya "parlama" efekti için ::before */
.servis-cagir-btn::before {
  content: '';
  position: absolute;
  width: 150%; /* Butondan daha büyük bir alan kaplar */
  height: 150%;
  background: conic-gradient(from 0deg at 50% 50%, 
                             rgba(255, 255, 255, 0) 0%, 
                             rgba(255, 255, 255, 0) 25%, 
                             rgba(255, 255, 255, 0.7) 50%, /* Parlaklık noktası */
                             rgba(255, 255, 255, 0) 75%, 
                             rgba(255, 255, 255, 0) 100%);
  border-radius: 50%; /* Yuvarlak maske */
  top: -25%; /* Butonun dışından başlaması için */
  left: -25%;
  animation: rotateStar 5s linear infinite; /* Dönme animasyonu */
  z-index: -1; /* Butonun metninin ve ana background'un altında kalır */
  opacity: 0.6; /* Parlaklığı biraz düşürülür */
}

/* Beyaz border efekti için ::after */
.servis-cagir-btn::after {
  content: '';
  position: absolute;
  width: calc(100% - 20px); /* 10px içeriden bir border efekti (border boyutu) */
  height: calc(100% - 20px);
  border: 10px solid rgba(255, 255, 255, 0.9); /* Beyaz border */
  border-radius: 50%;
  z-index: 0; /* ::before ve span arasında */
  box-sizing: border-box; /* padding ve border hesaba katılır */
  pointer-events: none; /* Tıklama olaylarını engeller, butona tıklanmasını sağlar */
}

/* Butonun üzerindeki yazı için span */
.servis-cagir-btn span {
  position: relative;
  z-index: 2; /* Tüm animasyonların üzerinde kalması için */
}

/* Hover efektleri */
.servis-cagir-btn:hover {
  transform: scale(1.08); /* Hafifçe büyür */
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4); /* Gölge belirginleşir */
}

/* Animasyonlar */

/* 1. Yumuşak renk geçişi (ana buton arka planı) */
@keyframes gradientShift {
  0% {
      background-position: 0% 50%;
  }
  50% {
      background-position: 100% 50%;
  }
  100% {
      background-position: 0% 50%;
  }
}

/* 2. "Yıldız Kayması" veya "Parlaklık Noktası" dönme animasyonu */
@keyframes rotateStar {
  0% {
      transform: rotate(0deg);
  }
  100% {
      transform: rotate(360deg);
  }
}

/* Opsiyonel: Butona basıldığında efekt */
.servis-cagir-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}
.uk-navbar-nav>li.uk-active>a{ color: #000; }
.uk-navbar-nav>li:hover>a, .uk-navbar-nav>li>a[aria-expanded=true]{ color: #000;}
.uk-navbar-dropdown{ min-width: 300px !important;}
.uk-navbar-dropdown-nav>li>a{ color: #000; font-size: 15px;}
.uk-navbar-dropdown-nav>li>a:hover{ color: #000; }
.uk-button-text{ color: #000 !important;}
.uk-navbar-item, .uk-navbar-nav>li>a, .uk-navbar-toggle{ text-transform:capitalize; min-height: 55px !important;}
.uk-navbar-nav>li>a{font-family: 'Manrope';}

.uk-offcanvas-bar{ background:linear-gradient(135deg, #007bff 0%, #0056b3 100%);}
.uk-card-primary.uk-card-body .uk-nav-primary>li>a, .uk-card-primary>:not([class*=uk-card-media]) .uk-nav-primary>li>a, .uk-card-secondary.uk-card-body .uk-nav-primary>li>a, .uk-card-secondary>:not([class*=uk-card-media]) .uk-nav-primary>li>a, .uk-light .uk-nav-primary>li>a, .uk-offcanvas-bar .uk-nav-primary>li>a, .uk-overlay-primary .uk-nav-primary>li>a, .uk-section-primary:not(.uk-preserve-color) .uk-nav-primary>li>a, .uk-section-secondary:not(.uk-preserve-color) .uk-nav-primary>li>a, .uk-tile-primary:not(.uk-preserve-color) .uk-nav-primary>li>a, .uk-tile-secondary:not(.uk-preserve-color) .uk-nav-primary>li>a{ color: #fff !important;}
.uk-offcanvas-overlay::before{background: rgba(0, 0, 0, 0.9);}
.bob {
  animation: bob 4s ease-in-out infinite;
}
@keyframes bob {
  0%, 100% {
      transform: translateY(0);
  }
  50% {
      transform: translateY(-20px);
  }
}
.br-20{border-radius: 20px;}
.br-10{border-radius: 10px;}
.br-10-top{border-radius: 10px 10px 0 0;}
.footer-bg{background: url('/images/footer-bg.jpg') no-repeat center right; background-size: cover; padding: 80px 0 80px 0;}
.product-list-title{ font-size: 18px; color: #000; z-index: 6; position: relative; }
.whatsapp-button{z-index: 9; bottom: 10px; right: 10px; display: inline-block; position: fixed; background: #1cb367; padding: 15px; color: #fff;}

.uk-breadcrumb>*>*{ color: rgb(182, 182, 182);}

.uk-padding-10{padding: 10px;}
.uk-margin-20{margin: 20px !important;}
.uk-margin-70-20{margin: 70px 20px !important;}

.uk-offcanvas-bar{ width: 300px;}
.uk-card-primary.uk-card-body .uk-nav-primary .uk-nav-sub a, .uk-card-primary>:not([class*=uk-card-media]) .uk-nav-primary .uk-nav-sub a, .uk-card-secondary.uk-card-body .uk-nav-primary .uk-nav-sub a, .uk-card-secondary>:not([class*=uk-card-media]) .uk-nav-primary .uk-nav-sub a, .uk-light .uk-nav-primary .uk-nav-sub a, .uk-offcanvas-bar .uk-nav-primary .uk-nav-sub a, .uk-overlay-primary .uk-nav-primary .uk-nav-sub a, .uk-section-primary:not(.uk-preserve-color) .uk-nav-primary .uk-nav-sub a, .uk-section-secondary:not(.uk-preserve-color) .uk-nav-primary .uk-nav-sub a, .uk-tile-primary:not(.uk-preserve-color) .uk-nav-primary .uk-nav-sub a, .uk-tile-secondary:not(.uk-preserve-color) .uk-nav-primary .uk-nav-sub a{ color: #fff !important;margin:5px 0; font-size: 15px; border-left: solid 4px #15bb4c; padding-left: 10px;}
.uk-navbar-nav>li.uk-active>a{ color: #000; }
.uk-navbar-nav>li:hover>a, .uk-navbar-nav>li>a[aria-expanded=true]{ color: #000;}
.uk-navbar-dropdown{ min-width: 300px !important;}
.uk-navbar-dropdown-nav>li>a{ color: #000; font-size: 15px;}
.uk-navbar-dropdown-nav>li>a:hover{ color: #000; }
.uk-button-text{ color: #000 !important;}
.uk-navbar-item, .uk-navbar-nav>li>a, .uk-navbar-toggle{ text-transform:capitalize; min-height: 55px !important;}



.box-pd{padding: 10px 7px 0px 7px !important;}

/* Simple Slider Yükseklik */
.simple-slider .uk-height-medium {
    height: 450px !important;
}

@media (min-width: 768px) {
    .simple-slider .uk-height-medium {
        height: 550px !important;
    }
}

@media (max-width: 767px) {
    .simple-slider .uk-height-medium {
        height: 400px !important;
    }
}

/* Tab Grid Margin Düzeltmesi */
.uk-switcher .uk-grid-margin {
    margin-top: 10px !important;
}

.uk-switcher .uk-grid > * {
    margin-top: 10px !important;
}

/* Servis kartları için özel margin */
.uk-switcher .uk-card {
    margin-bottom: 0 !important;
}

/* Simple Slider Stilleri */
.simple-slider .uk-overlay-primary {
    background: rgba(0, 0, 0, 0.4);
}

.simple-slider h2 {
    font-size: 2rem;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.simple-slider p {
    font-size: 1.1rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* Responsive düzenlemeler */
@media (min-width: 768px) {
    .simple-slider h2 {
        font-size: 2.5rem;
    }

    .simple-slider p {
        font-size: 1.2rem;
    }
}

@media (max-width: 767px) {
    .simple-slider h2 {
        font-size: 1.5rem;
    }

    .simple-slider p {
        font-size: 1rem;
    }
}

/* Ana Sayfa Özel Stilleri */
.uk-card-hover:hover {
    transform: translateY(-5px);
    transition: all 0.3s ease;
}

.counter {
    font-size: 2.5rem;
    font-weight: 700;
    color: #fff;
}

.uk-section-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.uk-section-secondary {
    background: linear-gradient(135deg, #343a40 0%, #495057 100%);
}

/* Hizmet kartları */
.uk-card-default {
    border: 1px solid #e5e5e5;
    transition: all 0.3s ease;
}

.uk-card-default:hover {
    border-color: #007bff;
    box-shadow: 0 10px 30px rgba(0, 123, 255, 0.1);
}

/* İstatistik animasyonu */
@keyframes countUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.counter {
    animation: countUp 0.8s ease-out;
}

/* Footer stilleri */
footer h4 {
    color: #fff;
    margin-bottom: 20px;
    font-weight: 600;
}

footer .uk-list > li {
    padding: 5px 0;
}

footer .uk-link-muted:hover {
    color: #007bff !important;
}

/* Responsive düzenlemeler */
@media (max-width: 767px) {
    .uk-text-large {
        font-size: 1.1rem;
    }

    .counter {
        font-size: 2rem;
    }

    .uk-card-body {
        padding: 20px;
    }
}


.center-bg{background: url('/images/center-bg.webp') no-repeat center bottom; background-size: 100%; padding: 100px 0 100px 0;}



.img-fit-320{object-fit: contain; height: 280px !important; width:100%; padding: 20px;}

.header-absolute{-moz-transition:all 0.2s ease-out;-o-transition:all 0.2s ease-out;-webkit-transition:all 0.2s ease-out;-ms-transition:all 0.2s ease-out;-transition:all 0.2s ease-out; z-index: 80; width: 100%; position: absolute; }

.uk-w-100{ width: 100% !important;}
.h-80-vh{height: 80vh;}
.h-100{ height: 100%;}
.h-100px{height: 100px;}
.slide-gradiend{position: absolute;background: rgb(13,23,27);
background: linear-gradient(24deg, rgba(13,23,27,1) 0%, rgba(39,68,83,0.7) 100%); width: 100%; height: 100%; z-index: 2; top: 0px;}
.slide-mask{position: absolute; z-index: 2; width:100%; bottom: -2px;}

.footer-menu li { display: block;}
.footer-menu li a{ display: block; color: #fff; text-align: left; font-size: 15px; font-weight: 400; margin: 14px 0 14px 0;}
.footer-menu li a:hover{ color: #084455;}

.home-product-category li { display: block;}
.home-product-category li a{ display: block; color: #fff; text-align: left; font-size: 20px; line-height: 45px; font-weight: 200;}
.home-product-category li a:hover{ color: #d1942a;}

.sidenav-menu li { display: block;}
.sidenav-menu li a:hover{ color: #d1942a;border-left: solid 4px #d1942a;}

.left-menu li { display: block;}
.left-menu li a{ display: block; color: #000; text-align: left; font-size: 15px; line-height: 15px; padding-left: 10px; font-weight: 500; border-left: solid 4px rgba(255, 255, 255, 0); margin: 0px 0px 15px 15px;}

.left-menu li .act{ color: #d1942a;border-left: solid 4px #d1942a;}
.left-menu li a:hover{ color: #d1942a;border-left: solid 4px #d1942a;}



.overlaymenu{ position:absolute; background:#0f1014; opacity:0.95; z-index:1; top:0px; left:0px;  width:100%; height:100%; display:none;	}

.sidenav { height: 100%; position: fixed;  z-index: 1; top: 0px;  transition: 0.5s;  }

.w-650{height: 100% !important;}



.bar1, .bar2, .bar3 {
  width: 55px;
  height: 1px;
  background-color: #fff;
  margin: 5px 0px 5px 0px;
  transition: 0.4s;
}

.change .bar1 {
  -webkit-transform: rotate(45deg) translate(-6px, 5px);
  transform: rotate(-45deg) translate(-6px, 5px);
  background-color: #fff;
}

.change .bar2 {opacity: 0;}

.change .bar3 {
  -webkit-transform: rotate(45deg) translate(-4px, -4px);
  transform: rotate(45deg) translate(-4px, -4px);background-color: #fff;
}

.overflow-y{ overflow-y:hidden; overflow-x: hidden;}
body{margin:0px; font-family: 'Manrope'; font-weight:400;font-size:16px;color:#2f2f2f; outline:none; }
html{overflow-y:scroll;overflow-x:hidden;height:100%;}



.ul{ list-style:none; padding:0px; margin:0px;}
.compensate-for-scrollbar{margin-right:0px !important;}

body::-webkit-scrollbar {
  width: 10px;
}
 
body::-webkit-scrollbar-track {
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
}
 
body::-webkit-scrollbar-thumb {
  background-color: #0441f0;
  outline: 1px solid #0441f0;
}
.uk-text-white{ color: #fff !important;}
.uk-text-grey{ color: #848484 !important;}

.uk-text-white-link{ color: #fff !important;}
.uk-text-white-link:hover{ color: #c5caca !important;}

.uk-text-yellow{ color: #d1942a !important;}
.uk-text-yellow:hover{ color: #fff !important;}

a{text-decoration:none;color:#85888a;-moz-transition:all 0.2s ease-out;-o-transition:all 0.2s ease-out;-webkit-transition:all 0.2s ease-out;-ms-transition:all 0.2s ease-out;-transition:all 0.2s ease-out;}
a:hover{text-decoration:none;color:#c7c7c7;-moz-transition:all 0.2s ease-out;-o-transition:all 0.2s ease-out;-webkit-transition:all 0.2s ease-out;-ms-transition:all 0.2s ease-out;-transition:all 0.2s ease-out;}
.uk-text-black{ color: #000 !important;}
.uk-text-blue{ color: #0042ff !important;}
.uk-text-green{ color: #2ab318 !important;}
h1{border:0;margin:0;padding:0;font-weight:normal; font-family: 'ClashDisplay-Semibold';}
h2,h3,h4,h5,h6{border:0;margin:0;padding:0;font-weight:normal; font-family: 'ClashDisplay-Medium' !important;}
.z-1{ z-index:1;}
.z-99{ z-index:99;}
.fw9{font-weight:900;}
.fw7{font-weight:700;}
.fw5{font-weight:500;}
.fw6{font-weight:600;}
.fw8{font-weight:800;}
.fw4{font-weight:400 !important;}
.fw3{font-weight:300 !important;}
.fw2{font-weight:200;}
.fw1{font-weight:100;}
.fs7{font-size:7px;}
.fs11{font-size:11px !important;}
.fs12{font-size:12px !important;}
.fs13{font-size:13px !important;}
.fs14{font-size:14px !important;}
.fs15{font-size:15px !important;}
.fs16{font-size:16px !important;}
.fs18{font-size:18px;}
.fs20{font-size:20px !important;}
.fs23{font-size:23px;}
.fs25{font-size:25px;}
.fs26{font-size:26px;}
.fs27{font-size:27px;}
.fs28{font-size:28px;}
.fs29{font-size:29px;}
.fs30{font-size:30px;}
.fs35{font-size:35px;}
.fs40{font-size:40px;}
.fs45{font-size:45px;}
.fs50{font-size:50px;}
.fs55{font-size:55px;}
.fs60{font-size:60px;}
.fs65{font-size:65px;}
.fs70{font-size:70px;}

.opacity-01{ opacity: 0.1;}
.opacity-02{ opacity: 0.2;}
.opacity-03{ opacity: 0.3;}
.opacity-04{ opacity: 0.4;}
.opacity-05{ opacity: 0.5;}
.opacity-06{ opacity: 0.6;}
.opacity-07{ opacity: 0.7;}
.opacity-08{ opacity: 0.8;}
.opacity-09{ opacity: 0.9;}


.hover-top{-moz-transition:all 0.2s ease-out;-o-transition:all 0.2s ease-out;-webkit-transition:all 0.2s ease-out;-ms-transition:all 0.2s ease-out;}
.hover-top:hover{transform:translateY(-3px); -moz-transition:all 0.2s ease-out;-o-transition:all 0.2s ease-out;-webkit-transition:all 0.2s ease-out;-ms-transition:all 0.2s ease-out;}

.hover-top-shadow{-moz-transition:all 0.2s ease-out;-o-transition:all 0.2s ease-out;-webkit-transition:all 0.2s ease-out;-ms-transition:all 0.2s ease-out;}
.hover-top-shadow:hover{ box-shadow: 0px 6px 22px #ebebeb; transform:translateY(-3px); -moz-transition:all 0.2s ease-out;-o-transition:all 0.2s ease-out;-webkit-transition:all 0.2s ease-out;-ms-transition:all 0.2s ease-out;}

.hover-top2{-moz-transition:all 0.2s ease-out;-o-transition:all 0.2s ease-out;-webkit-transition:all 0.2s ease-out;-ms-transition:all 0.2s ease-out;}
.hover-top2:hover{transform:translateY(-3px); -moz-transition:all 0.2s ease-out;-o-transition:all 0.2s ease-out;-webkit-transition:all 0.2s ease-out;-ms-transition:all 0.2s ease-out;}

/* Hemen Ara ve WhatsApp Butonları */
.contact-buttons {
  display: flex;
  gap: 15px;
  margin: 30px 0;
  justify-content: center;
}

.contact-buttons .contact-btn {
  flex: 1;
  max-width: 200px;
  padding: 15px 20px;
  border-radius: 10px;
  text-decoration: none;
  color: #fff;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 16px;
}

.contact-buttons .call-btn {
  background: linear-gradient(135deg, #007bff, #0056b3);
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.contact-buttons .whatsapp-btn {
  background: linear-gradient(135deg, #25d366, #1cb367);
  box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
}

.contact-buttons .contact-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  color: #fff;
}

/* Mobil için fixed butonlar */
@media (max-width: 959px) {
  .contact-buttons {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    margin: 0;
    padding: 10px;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 9;
    gap: 10px;
  }

  .contact-buttons .contact-btn {
    max-width: none;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 14px;
  }

  /* Mobilde body'ye padding ekle ki butonlar içeriği kapatmasın */
  body {
    padding-bottom: 70px;
  }
}

/* Masaüstünde normal görünüm */
@media (min-width: 960px) {
  .contact-buttons {
    max-width: 500px;
    margin: 30px auto;
  }
}

/* Hero Slider Stilleri */
.hero-slider-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.hero-content {
  background: #fff;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border-left: 5px solid #007bff;
}

.hero-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1.2;
  margin-bottom: 20px;
}

.hero-subtitle {
  color: #6c757d;
  font-size: 1.2rem;
  line-height: 1.6;
  font-weight: 400;
}

/* Slider özel stilleri */
.uk-slider-nav {
  margin-top: 20px;
}



/* Responsive düzenlemeler */
@media (max-width: 959px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .hero-content {
    border-left: none;
    border-top: 5px solid #007bff;
  }

  .uk-height-large {
    height: 300px !important;
  }
}

@media (min-width: 960px) {
  .uk-height-large {
    height: 500px !important;
  }
}

/* manrope-200 - cyrillic_cyrillic-ext_greek_latin_latin-ext_vietnamese */
@font-face {
  font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
  font-family: 'Manrope';
  font-style: normal;
  font-weight: 200;
  src: url('/fonts/manrope-v15-cyrillic_cyrillic-ext_greek_latin_latin-ext_vietnamese-200.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
}
/* manrope-300 - cyrillic_cyrillic-ext_greek_latin_latin-ext_vietnamese */
@font-face {
  font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
  font-family: 'Manrope';
  font-style: normal;
  font-weight: 300;
  src: url('/fonts/manrope-v15-cyrillic_cyrillic-ext_greek_latin_latin-ext_vietnamese-300.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
}
/* manrope-regular - cyrillic_cyrillic-ext_greek_latin_latin-ext_vietnamese */
@font-face {
  font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
  font-family: 'Manrope';
  font-style: normal;
  font-weight: 400;
  src: url('/fonts/manrope-v15-cyrillic_cyrillic-ext_greek_latin_latin-ext_vietnamese-regular.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
}
/* manrope-500 - cyrillic_cyrillic-ext_greek_latin_latin-ext_vietnamese */
@font-face {
  font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
  font-family: 'Manrope';
  font-style: normal;
  font-weight: 500;
  src: url('/fonts/manrope-v15-cyrillic_cyrillic-ext_greek_latin_latin-ext_vietnamese-500.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
}
/* manrope-600 - cyrillic_cyrillic-ext_greek_latin_latin-ext_vietnamese */
@font-face {
  font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
  font-family: 'Manrope';
  font-style: normal;
  font-weight: 600;
  src: url('/fonts/manrope-v15-cyrillic_cyrillic-ext_greek_latin_latin-ext_vietnamese-600.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
}
/* manrope-700 - cyrillic_cyrillic-ext_greek_latin_latin-ext_vietnamese */
@font-face {
  font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
  font-family: 'Manrope';
  font-style: normal;
  font-weight: 700;
  src: url('/fonts/manrope-v15-cyrillic_cyrillic-ext_greek_latin_latin-ext_vietnamese-700.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
}
/* manrope-800 - cyrillic_cyrillic-ext_greek_latin_latin-ext_vietnamese */
@font-face {
  font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
  font-family: 'Manrope';
  font-style: normal;
  font-weight: 800;
  src: url('/fonts/manrope-v15-cyrillic_cyrillic-ext_greek_latin_latin-ext_vietnamese-800.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
}

.font2{font-family: 'ClashDisplay-Extralight';}
.font3{font-family: 'ClashDisplay-Light';}
.font4{font-family: 'ClashDisplay-Regular';}
.font5{font-family: 'ClashDisplay-Medium';}
.font6{font-family: 'ClashDisplay-Semibold';}
.font7{font-family: 'ClashDisplay-Bold';}

@font-face {
  font-family: 'ClashDisplay-Extralight';
  src: url('/fonts/ClashDisplay-Extralight.woff2') format('woff2'),
       url('/fonts/ClashDisplay-Extralight.woff') format('woff'),
       url('/fonts/ClashDisplay-Extralight.ttf') format('truetype');
  font-weight: 200;
  font-display: swap;
  font-style: normal;
}
@font-face {
  font-family: 'ClashDisplay-Light';
  src: url('/fonts/ClashDisplay-Light.woff2') format('woff2'),
       url('/fonts/ClashDisplay-Light.woff') format('woff'),
       url('/fonts/ClashDisplay-Light.ttf') format('truetype');
  font-weight: 300;
  font-display: swap;
  font-style: normal;
}
@font-face {
  font-family: 'ClashDisplay-Regular';
  src: url('/fonts/ClashDisplay-Regular.woff2') format('woff2'),
       url('/fonts/ClashDisplay-Regular.woff') format('woff'),
       url('/fonts/ClashDisplay-Regular.ttf') format('truetype');
  font-weight: 400;
  font-display: swap;
  font-style: normal;
}
@font-face {
  font-family: 'ClashDisplay-Medium';
  src: url('/fonts/ClashDisplay-Medium.woff2') format('woff2'),
       url('/fonts/ClashDisplay-Medium.woff') format('woff'),
       url('/fonts/ClashDisplay-Medium.ttf') format('truetype');
  font-weight: 500;
  font-display: swap;
  font-style: normal;
}
@font-face {
  font-family: 'ClashDisplay-Semibold';
  src: url('/fonts/ClashDisplay-Semibold.woff2') format('woff2'),
       url('/fonts/ClashDisplay-Semibold.woff') format('woff'),
       url('/fonts/ClashDisplay-Semibold.ttf') format('truetype');
  font-weight: 600;
  font-display: swap;
  font-style: normal;
}
@font-face {
  font-family: 'ClashDisplay-Bold';
  src: url('/fonts/ClashDisplay-Bold.woff2') format('woff2'),
       url('/fonts/ClashDisplay-Bold.woff') format('woff'),
       url('/fonts/ClashDisplay-Bold.ttf') format('truetype');
  font-weight: 700;
  font-display: swap;
  font-style: normal;
}
/**
* This is a variable font
* You can control variable axes as shown below:
* font-variation-settings: wght 700.0;
*
* available axes:
'wght' (range from 200.0 to 700.0
*/
@font-face {
  font-family: 'ClashDisplay-Variable';
  src: url('/fonts/ClashDisplay-Variable.woff2') format('woff2'),
       url('/fonts/ClashDisplay-Variable.woff') format('woff'),
       url('/fonts/ClashDisplay-Variable.ttf') format('truetype');
  font-weight: 200 700;
  font-display: swap;
  font-style: normal;
}