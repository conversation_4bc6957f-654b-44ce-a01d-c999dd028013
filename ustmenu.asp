<!--#include file="view.asp"-->
<%
id=sec.g("id")
if id<>"" then
	id=kayitGetir("sayfalar","seo_"&dil &"='"& id &"' and sayfaTIP=1","id")
	if id="" then id=0
else
	id=sec.gint("id")
end if
set duy1=bag.execute("select * from sayfalar where sayfaTIP=1 and id="&id&" and aktifpasif=true order by sira")
set kbaslik=bag.execute("select * from sayfalar where sayfaTIP=1 and id="& duy1("uid") &" and aktifpasif=true order by sira")

if not kbaslik.eof then
    set kust=bag.execute("select * from sayfalar where sayfaTIP=1 and id="& k<PERSON>lik("uid") &" and aktifpasif=true order by sira")
end if


if duy1("title_"&dil)<>"" then
title=duy1("title_"&dil)
else
title=duy1("baslik_"&dil)
end if

if duy1("description_"&dil)<>"" then
description1=duy1("description_"&dil)
else
description1=duy1("baslik_"&dil)
end if

url="/"& duy1("seo_"& dil)

if duy1("resim")<>"" then
    resimyol=duy1("resim")
else
    resimyol=ayargetir("resim2")
end if

call header%>
<div>
    <img src="/<%=resimyol%>" alt="Kayseri Beyaz Eşya Servisi - İklim Servis Profesyonel Tamir Hizmeti" class="uk-width-1-1 br-20 uk-box-shadow-xlarge" title="Kayseri'nin En Güvenilir Beyaz Eşya Servisi" loading="eager">
</div>
<h1 class="ic-title"><%=duy1("baslik_"&dil)%></h1>
    <div>
        <ul class="uk-breadcrumb uk-text-center uk-margin-medium-bottom">
            <li itemprop="itemListElement" itemscope itemtype="http://schema.org/ListItem"> <a itemscope itemtype="http://schema.org/Thing" id="1" itemprop="item" href="/" title="Anasayfa"> <span itemprop="name">Anasayfa</span> </a> <meta itemprop="position" content="1"></li>
            <%if not kbaslik.eof then%>
            <li itemprop="itemListElement" itemscope itemtype="http://schema.org/ListItem"> <a itemscope itemtype="http://schema.org/Thing" id="2" itemprop="item" href="/<%=kbaslik("seo_"& dil)%>" title="<%=kbaslik("baslik_"& dil)%>"> <span itemprop="name"><%=kbaslik("baslik_"& dil)%></span><meta itemprop="position" content="2"></a></li>
            <%end if%>
            <li itemprop="itemListElement" itemscope itemtype="http://schema.org/ListItem" class="<%if not kbaslik.eof then%>uk-visible@s<%end if%>"> <a itemscope itemtype="http://schema.org/Thing" id="<%if not kbaslik.eof then%>3<%else%>2<%end if%>" itemprop="item" href="/<%=duy1("seo_"& dil)%>" title="<%=duy1("baslik_"& dil)%>"> <span itemprop="name"><%=duy1("baslik_"& dil)%></span></a><meta itemprop="position" content="<%if not kbaslik.eof then%>3<%else%>2<%end if%>"> </li>
        </ul>
    </div>
<div class="uk-container uk-container-large uk-margin-large-top uk-margin-large-bottom">
    <div uk-scrollspy="cls: uk-animation-fade; target: div; delay: 100; repeat: false;">
    <div class="uk-margin"><%=duy1("icerik_"&dil)%></div>
    </div>
    <%set ustkat=bag.execute("select * from sayfalar where sayfaTIP=1 and uid="& duy1("id") &" and aktifpasif=true order by sira")
    if not ustkat.eof then
    %>
    <div class="uk-child-width-1-3@l uk-child-width-1-2 uk-text-center uk-grid-match" uk-grid uk-scrollspy="cls: uk-animation-fade; target: div; delay: 50; repeat: false;">
        <%while not ustkat.eof
        if ustkat("resim")<>"" then
            uresyol=ustkat("resim")
        else
            uresyol="images/nofoto.jpg"
        end if
        %>
        <a href="/<%=ustkat("seo_"&dil)%>" class="hover-top uk-display-block">
            <div><img class="br-10" width="700" height="400" src="/<%=uresyol%>" alt="<%=ustkat("baslik_"&dil)%>" style="object-fit:cover; height:200px"></div>
            <div class="uk-margin-small-top fs18"><%=ustkat("baslik_"& dil)%></div>
        </a>
        <%ustkat.movenext
        wend%>
    </div>
    <%end if%>
    <%set dokumanlar=bag.execute("select * from menudosya where uid="& duy1("id") &" order by sira")
    if not dokumanlar.eof then
    %>
    <div class="uk-margin-medium-bottom uk-margin-medium-top">
        <%
        while not dokumanlar.eof
        yol2=dokumanlar("resim")		
        %>
        <div class="uk-margin-small-bottom">
            <a href="/<%=dokumanlar("resim")%>" target="_blank" class="uk-box-shadow-xlarge hover-top uk-display-block uk-text-secondary"><span class=" uk-padding-small uk-margin-small-right text-red uk-text-large" uk-icon="icon:download;ratio: 2"></span><%=dokumanlar("ad_"&dil)%></a>
        </div>
        <%dokumanlar.movenext
        wend%>  
    </div>     
    <%end if%>

    <%set menures=bag.execute("select * from menuresim where uid="& duy1("id") &" order by sira")
    if not menures.eof then%>
    <div class="uk-child-width-1-4@l uk-child-width-1-3@m uk-child-width-1-2 uk-margin-large-top" uk-grid uk-lightbox="animation: scale"  uk-scrollspy="cls: uk-animation-fade; target: div; delay: 100; repeat: false;">
        <%
        while not menures.eof
        if menures("resim")<>"" then
        ryol=menures("resim")
        else
        ryol="images/nofoto.jpg"
        end if
        %>
        <div>
            <a class="uk-inline uk-box-shadow-small uk-w-100 hover-top uk-background-default uk-border-rounded" href="/<%=ryol%>" data-caption="<%=menures("ad_"& dil)%>">
                <img src="/<%=ryol%>" width="1800" height="1200" alt="" class="uk-border-rounded img-fit-320">
            </a>
            <div class="uk-margin-small-top uk-text-center">
                <%=menures("ad_"&dil)%>
            </div>
        </div>          
        <%menures.movenext
        wend%>
        </div>         
    <%end if%>

    <%
    set sss=bag.execute("select * from aciliricerik where aid="& duy1("id") &" order by sira")
    if not sss.eof then
    %>
    <ul uk-accordion class="uk-margin-medium-top">
        <%i=1
        while not sss.eof
        %>
        <li class="<%if i=1 then%>uk-open1<%end if%> uk-box-shadow-small uk-padding-small">
            <a class="uk-accordion-title" href="#"><%=sss("baslik_"&dil)%></a>
            <div class="uk-accordion-content">
                <p><%=sss("icerik_"&dil)%></p>

                <%set menures=bag.execute("select * from acilirresim where uid="& sss("id") &" order by sira")
                if not menures.eof then%>
                <div class="uk-child-width-1-5@l uk-child-width-1-4@m uk-child-width-1-2 " uk-grid uk-lightbox="animation: scale">
                    <%
                    while not menures.eof
                    if menures("resim")<>"" then
                    ryol=menures("resim")
                    else
                    ryol="images/nofoto.jpg"
                    end if
                    %>       
                    <div>
                        <a class="uk-inline uk-box-shadow-small uk-w-100 hover-top" href="/<%=ryol%>" data-caption="<%=menures("ad_"& dil)%>">
                            <img src="/<%=ryol%>" width="1800" height="1200" alt="" class="img-fit-320">
                        </a>
                    </div>           
                    <%menures.movenext
                    wend%>
                    </div>         
                <%end if%>   

            </div>
        </li>
        <%sss.movenext
        i=i+1
        wend
        sss.close%>
    </ul>
    <%end if%> 

    <%set urunler=bag.execute("select * from urunler where aid="& duy1("id") &" order by sira")
    if not urunler.eof then%>
    <div class="uk-child-width-1-3@l uk-child-width-1-2@s uk-text-center uk-grid-match" uk-grid uk-scrollspy="cls: uk-animation-fade; target: div; delay: 50; repeat: false;">
        <%while not urunler.eof
        if urunler("resim")<>"" then
            uyol=urunler("resim")
        else
            uyol="images/nofoto.jpg"
        end if
        %>
        <div>
            <div class="uk-inline-clip uk-transition-toggle uk-border-rounded hover-top-shadow uk-padding-remove uk-box-shadow-small">
                <a href="/<%=strurunlink%>/<%=urunler("seo_"&dil)%>" class="uk-padding-small">
                    <div>
                        <div class="uk-text-center uk-position-relative">
                            <img src="/<%=uyol%>" width="1800" height="100" alt="<%=urunler("baslik_"&dil)%>" class="uk-border-rounded img-fit-320">
                            <%if urunler("resim2")<>"" then%>
                            <img class="uk-transition-scale-up uk-position-center img-fit-320" src="/<%=urunler("resim2")%>" alt="<%=urunler("baslik_"&dil)%>">
                            <%end if%>
                        </div>
                    </div>
                    <div class="product-list-title"><%=urunler("baslik_"&dil)%></div>
                </a>
            </div>
        </div>
        <%urunler.movenext
        wend
        urunler.close%>
    </div>
    <%end if%>    
</div>
<%call footer%>