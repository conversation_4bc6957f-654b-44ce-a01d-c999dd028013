﻿<!--#include file="view.asp"-->
<%
id=5
set duy1=bag.execute("select * from sayfalar where sayfaTIP=1 and id="&id&" and aktifpasif=true order by sira")
set kbaslik=bag.execute("select * from sayfalar where sayfaTIP=1 and id="& duy1("uid") &" and aktifpasif=true order by sira")

if not kbaslik.eof then
    set kust=bag.execute("select * from sayfalar where sayfaTIP=1 and id="& kbaslik("uid") &" and aktifpasif=true order by sira")
end if


if duy1("title_"&dil)<>"" then
title=duy1("title_"&dil)
else
title=duy1("baslik_"&dil)
end if

if duy1("description_"&dil)<>"" then
description1=duy1("description_"&dil)
else
description1=duy1("baslik_"&dil)
end if

url="/"& duy1("seo_"& dil)

if duy1("resim1")<>"" then
    resimyol=duy1("resim1")
else
    resimyol=ayargetir("resim2")
end if

call header%>
<div>
    <img src="/<%=resimyol%>" alt="Blog" class="uk-width-1-1 br-20 uk-box-shadow-xlarge" title="Blog" loading="eager">
</div>
<div class="uk-container uk-margin-medium-top">
    <h1 class="fs30 uk-text-center"><%=duy1("baslik_"&dil)%></h1>
    <div>
        <ul class="uk-breadcrumb uk-text-center">
            <li itemprop="itemListElement" itemscope itemtype="http://schema.org/ListItem">
                <a itemscope itemtype="http://schema.org/Thing" id="1" itemprop="item" href="/" title="Anasayfa">
                    <span itemprop="name">Anasayfa</span>
                </a>
                <meta itemprop="position" content="1">
            </li>
            <li itemprop="itemListElement" itemscope itemtype="http://schema.org/ListItem">
                <a itemscope itemtype="http://schema.org/Thing" id="2" itemprop="item" href="/<%=duy1("seo_"& dil)%>" title="<%=duy1("baslik_"&dil)%>">
                    <span itemprop="name"><%=duy1("baslik_"&dil)%></span>
                </a>
                <meta itemprop="position" content="2">
            </li>
        </ul>
    </div>
</div><hr>
<div style="min-height: 500px;" class="uk-container uk-container-large uk-margin-large-top uk-margin-large-bottom">
    <%set slogan=bag.execute("select * from sayfalar where sayfaTIP=4 and aktifpasif=true order by sira")%>
    <div class="uk-grid uk-grid-match uk-child-width-1-2@s uk-child-width-1-3@l" uk-grid>
    <%while not slogan.eof%>
        <div>
            <div class="uk-card uk-card-default uk-card-hover uk-height-1-1 br-10">
                <div class="uk-card-body">
                    <h3 class="fw5 uk-card-title fs20"><%=slogan("baslik_"&dil)%></h3>
                    <%if slogan("icerik_"&dil)<>"" then%>
                    <div class="fs15 uk-margin-small-top"><%=left(htmlcodeclear(slogan("icerik_"&dil)), 270)%>...</div>
                    <%end if%>
                    <div class="uk-margin-small-top">
                        <a href="/blog/<%=slogan("seo_"&dil)%>" class="uk-text-blue hover-top fs15" aria-label="<%=slogan("baslik_"&dil)%>">Devamını Oku</a>
                    </div>
                </div>
            </div>
        </div>
    <%slogan.movenext
    wend%>
    </div>
</div>
<%call footer%>