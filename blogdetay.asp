﻿<!--#include file="view.asp"-->
<%
id=sec.g("id")
if id<>"" then
	id=kayitGetir("sayfalar","seo_"&dil &"='"& id &"' and sayfaTIP=4","id")
	if id="" then id=0
else
	id=sec.gint("id")
end if
set duy1=bag.execute("select * from sayfalar where sayfaTIP=4 and id="&id&" and aktifpasif=true order by sira")

if duy1("title_"&dil)<>"" then
title=duy1("title_"&dil)
else
title=duy1("baslik_"&dil)
end if

if duy1("description_"&dil)<>"" then
description1=duy1("description_"&dil)
else
description1=duy1("baslik_"&dil)
end if

url="/blog/"& duy1("seo_"& dil)

if duy1("resim1")<>"" then
    resimyol=duy1("resim1")
else
    resimyol=ayargetir("resim2")
end if

call header%>
<div>
    <img src="/<%=resimyol%>" alt="Blog" class="uk-width-1-1 br-20 uk-box-shadow-xlarge" title="Blog" loading="eager">
</div>
<div class="uk-container uk-margin-medium-top">
    <h1 class="fs30 uk-text-center"><%=duy1("baslik_"&dil)%></h1>
    <div>
        <ul class="uk-breadcrumb uk-text-center uk-margin-medium-bottom">
            <li itemprop="itemListElement" itemscope itemtype="http://schema.org/ListItem"> <a itemscope itemtype="http://schema.org/Thing" id="1" itemprop="item" href="/" title="Anasayfa"> <span itemprop="name">Anasayfa</span> </a> <meta itemprop="position" content="1"></li>
            <li itemprop="itemListElement" itemscope itemtype="http://schema.org/ListItem"> <a itemscope itemtype="http://schema.org/Thing" id="2" itemprop="item" href="/blog" title="Blog"> <span itemprop="name">Blog</span></a><meta itemprop="position" content="2"> </li>
            <li itemprop="itemListElement" itemscope itemtype="http://schema.org/ListItem"><span itemprop="name"><%=duy1("baslik_"& dil)%></span><meta itemprop="position" content="3"> </li>
        </ul>
    </div>
</div><hr>

<div style="min-height: 500px;" class="uk-container uk-container-large uk-margin-large-top uk-margin-large-bottom">
    <%=duy1("icerik_"& dil)%>
</div>
<%call footer%>